package monitoring

type SecurityEventType string
type SecuritySeverity string

const (
	SecurityEventTypeAuthentication SecurityEventType = "authentication"
	SecurityEventTypeAuthorization SecurityEventType = "authorization"
	SecurityEventTypeAccess       SecurityEventType = "access"
)

const (
	SeverityInfo    SecuritySeverity = "info"
	SeverityLow     SecuritySeverity = "low"
	SeverityMedium  SecuritySeverity = "medium"
	SeverityHigh    SecuritySeverity = "high"
	SeverityCritical SecuritySeverity = "critical"
)

type SecurityEvent struct {
	EventType   SecurityEventType  `json:"event_type"`
	Severity    SecuritySeverity   `json:"severity"`
	Source      string            `json:"source"`
	UserID      string            `json:"user_id"`
	Description string            `json:"description"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
}
